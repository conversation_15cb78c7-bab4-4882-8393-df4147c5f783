package ui

import (
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputKeepDetail"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputKeepDetail"
	"deskcrm/libs/json"
	"deskcrm/service/innerapi/trade"
	"deskcrm/service/ui"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var StudentController studentController

type studentController struct {
}

// 获取学生列表
func (r studentController) GetStudentList(ctx *gin.Context) {
	// param
	var param inputStudent.StudentListParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	// service
	rsp, err := ui.StudentService.GetStudentList(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

func (r studentController) GetStudentListAPI(ctx *gin.Context) {
	// param
	var param inputStudent.StudentListParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	err := param.ValidateAPI(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	// service
	rsp, err := ui.StudentService.GetStudentList(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

func (r studentController) GetCourseTimeTableDay(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.CourseTimeTableDayParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 调用服务层
	rsp, err := ui.CourseTimeTableService.GetCourseTimeTableDay(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func (r studentController) GetCourseTimeTableWeek(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.CourseTimeTableWeekParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 调用服务层
	rsp, err := ui.CourseTimeTableService.GetCourseTimeTableWeek(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

// GetStudentOrderList 获取学生订单列表
// 迁移自 PHP 的 Api_StudentLogisticsInfo::getStudentOrderList 接口
func (r studentController) GetStudentOrderList(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.StudentOrderListParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 调用服务层
	rsp, err := trade.BillingService.GetStudentOrderList(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

// InterviewReferLpc 获取lpc侧家访参考信息
func (r studentController) InterviewReferLpc(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.InterviewReferLpcParam

	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 调用服务层
	rsp, err := ui.InterviewService.InterviewReferLpc(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

func (r studentController) GetStudentBind(ctx *gin.Context) {
	var param inputKeepDetail.GetStudentBindParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	resp, err := ui.KeepDetailService.GetStudentBind(ctx, param.CourseId, param.StudentUid)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) GetActiveWithBindData(ctx *gin.Context) {
	var param inputKeepDetail.GetActiveWithBindDataParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	resp, err := ui.KeepDetailService.GetActiveWithBindData(ctx, param.CourseId, param.StudentUid, param.AssistantUid, param.ApplyType)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) GetCustomFieldOptions(ctx *gin.Context) {
	var param inputKeepDetail.GetCustomFieldParams
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	resp, err := ui.KeepDetailService.GetCustomFieldOptions(ctx, param.CourseID, param.StudentUid)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) GetStudentCallInfo(ctx *gin.Context) {
	var param inputKeepDetail.StudentCallInfoParams
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	resp, err := ui.KeepDetailService.GetStudentCallInfo(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) GetContactFlagOption(ctx *gin.Context) {
	resp := outputKeepDetail.GetContactFlagOptionResp{ContactFlagOption: consts.ContactFlagNameMap}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) GetCallTypeList(ctx *gin.Context) {
	resp, err := ui.KeepDetailService.GetCallTypeList(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) CommonGray(ctx *gin.Context) {
	resp := outputKeepDetail.CommonGrayConfig{
		IsCommon:         1,
		IsCommonDetail:   1,
		LpcOldPersonUids: 1,
		DetailConfigGray: 0,
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (r studentController) StudentDelaminationDifferenceListV1(ctx *gin.Context) {
	var param inputStudent.StudentDelaminationDifferenceListV1Param
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "request param: %s", paramJson)

	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	resp, err := ui.StudentService.StudentDelaminationDifferenceListV1(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

// GetInterviewRecord 获取访谈记录
func (r studentController) GetInterviewRecord(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.InterviewRecordV2Param
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 调用服务层
	rsp, err := ui.InterviewService.GetInterviewRecord(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

// GetInterviewRecordV2 获取访谈记录V2
func (r studentController) GetInterviewRecordV2(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.InterviewRecordV2Param
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 调用服务层
	rsp, err := ui.InterviewService.GetInterviewRecordV2(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

// GetWxBindInfo 获取微信绑定信息
func (r studentController) GetWxBindInfo(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.GetWxBindInfoParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 记录请求参数
	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "GetWxBindInfo request param: %s", paramJson)

	// 调用服务层
	rsp, err := ui.StudentService.GetWxBindInfo(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}

// GetKeyBehavior 获取学生关键行为数据
func (r studentController) GetKeyBehavior(ctx *gin.Context) {
	// param
	var param inputStudent.KeyBehaviorParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}
	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "GetKeyBehavior request param: %s", paramJson)

	// service
	rsp, err := ui.KeyBehaviorService.GetKeyBehavior(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

// GetDetailPageOption 获取学员详情页面选项
func (r studentController) GetDetailPageOption(ctx *gin.Context) {
	// 调用服务层
	rsp, err := ui.StudentService.GetDetailPageOption(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

// GetCourseRecordDefaultOption 获取课程记录默认选项
func (r studentController) GetCourseRecordDefaultOption(ctx *gin.Context) {
	// 参数绑定和验证
	var param inputStudent.CourseRecordDefaultOptionParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 记录请求参数
	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "GetCourseRecordDefaultOption request param: %s", paramJson)

	// 调用服务层
	rsp, err := ui.StudentService.GetCourseRecordDefaultOption(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

// GetCourseRecordMeta 获取课程记录元数据
func (r studentController) GetCourseRecordMeta(ctx *gin.Context) {
	var param inputStudent.CourseRecordMetaParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "GetCourseRecordMeta request param: %s", paramJson)

	rsp, err := ui.CourseRecordMetaService.GetCourseRecordMeta(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}

// GetStudentCallRecordInfo 获取学生通话记录信息
func (r studentController) GetStudentCallRecordInfo(ctx *gin.Context) {
	// param
	var param inputStudent.GetStudentCallRecordInfoParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.InvalidParam(err.Error()))
		return
	}

	// 参数验证
	err := param.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	paramJson, _ := json.MarshalToString(param)
	zlog.Infof(ctx, "GetStudentCallRecordInfo request param: %s", paramJson)

	// service
	rsp, err := ui.StudentService.GetStudentCallRecordInfo(ctx, &param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
	return
}
